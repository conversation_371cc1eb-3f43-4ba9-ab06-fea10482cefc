import React, { useEffect, useRef, useState } from "react";
import QuillViewer from "./QuillViewer";
import { parseRulesContent } from "@/utils";
import { Divider } from "@heroui/divider";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useGlobalState } from "~~/services/store/store";

interface RulesItemProps {
  currentEvent: {
    rules?: string;
    event_markets?: {
      question_market?: {
        id?: string;
        uma_url?: string;
      };
    }[];
  };
  selectedItemId?: string | null;
}

// 检测内容是否为Quill Delta格式
const isDeltaFormat = (content: string): boolean => {
  try {
    const parsed = JSON.parse(content);
    return parsed && typeof parsed === "object" && Array.isArray(parsed.ops);
  } catch {
    return false;
  }
};

// 清理可能导致不换行的字符和格式
const cleanRulesContent = (content: string): string => {
  return (
    content
      // 移除行首的缩进空格，防止被当作代码块
      .replace(/^[ \t]+/gm, "")
      // 替换非断行空格为普通空格
      .replace(/\u00A0/g, " ")
      // 替换其他可能的不可见字符
      .replace(/\u2060/g, "") // Word Joiner
      .replace(/\uFEFF/g, "") // Zero Width No-Break Space
      // 清理多余的空白行
      .replace(/\n\s*\n\s*\n/g, "\n\n")
      // 去除首尾空白
      .trim()
  );
};

// 根据语言解析多语言内容
const parseMultiLanguageContent = (content: string, currentLanguage: string): string => {
  // 检查是否包含语言间隔符
  if (content.includes("predictionOne$")) {
    const languageMap: { [key: string]: string } = {
      en: "predictionOne$en",
      zh: "predictionOne$zh",
      ko: "predictionOne$ko",
    };

    const separator = languageMap[currentLanguage] || languageMap.en;
    const parts = content.split(separator);

    if (parts.length > 1) {
      // 找到对应语言的内容，取第二部分（分隔符后的内容）
      let languageContent = parts[1];

      // 移除其他语言的内容（找到下一个分隔符并截断）
      const otherSeparators = Object.values(languageMap).filter(sep => sep !== separator);
      for (const otherSep of otherSeparators) {
        const nextSepIndex = languageContent.indexOf(otherSep);
        if (nextSepIndex !== -1) {
          languageContent = languageContent.substring(0, nextSepIndex);
        }
      }

      return languageContent.trim();
    }
  }

  // 如果没有间隔符，返回原内容
  return content;
};

const RulesItem: React.FC<RulesItemProps> = ({ currentEvent, selectedItemId }) => {
  const { current_language } = useGlobalState().nativeCurrency;
  const [currentRules, setCurrentRules] = useState<string>("");
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [isDelta, setIsDelta] = useState<boolean>(false);
  const [needsExpansion, setNeedsExpansion] = useState<boolean>(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // 如果只有一个question，默认展示第一个；否则根据选中状态匹配
  const getUmaUrl = () => {
    const markets = currentEvent?.event_markets || [];

    if (markets.length === 1) {
      // 只有一个question时，直接使用第一个
      return markets[0]?.question_market?.uma_url || null;
    } else {
      // 多个question时，根据选中状态匹配
      const matchedMarket = markets.find(market => market.question_market?.id === selectedItemId);
      return matchedMarket?.question_market?.uma_url || null;
    }
  };

  const uma_url = getUmaUrl();

  const getTransactionHash = (url: string | null): string | null => {
    if (!url) return null;
    const match = url.match(/transactionHash=([^&]+)/);
    return match ? `${match[1].slice(0, 12)}...` : null;
  };

  const transactionHash = getTransactionHash(uma_url);

  useEffect(() => {
    if (currentEvent?.rules) {
      try {
        const decodedRules = parseRulesContent(currentEvent.rules);

        // 解析多语言内容
        const languageSpecificContent = parseMultiLanguageContent(decodedRules, current_language);

        // 检测是否为Delta格式
        const isQuillDelta = isDeltaFormat(languageSpecificContent);
        setIsDelta(isQuillDelta);

        // 如果不是Delta格式，清理内容以防止换行问题
        const finalContent = isQuillDelta ? languageSpecificContent : cleanRulesContent(languageSpecificContent);

        setCurrentRules(finalContent);
      } catch (error) {
        console.error("Error decoding rules content:", error);
        setCurrentRules("Error decoding rules content.");
        setIsDelta(false);
      }
    }
  }, [current_language, currentEvent?.rules]);

  // 检查内容是否需要展开按钮 - 基于字符数量
  useEffect(() => {
    if (currentRules) {
      // 移除HTML标签和多余空白，获取纯文本内容
      const textContent = currentRules
        .replace(/<[^>]*>/g, "") // 移除HTML标签
        .replace(/\s+/g, " ") // 将多个空白字符替换为单个空格
        .trim();

      // 根据字符数量判断是否需要展开按钮
      // 一般来说，3行大约可以显示150-200个字符（包括中文、韩文等）
      const characterThreshold = 150;
      const needsExpand = textContent.length > characterThreshold;

      setNeedsExpansion(needsExpand);

      // 调试信息
      console.log("Rules content length:", textContent.length);
      console.log("Needs expansion:", needsExpand);
      console.log("Text preview:", textContent.substring(0, 100) + "...");
      console.log("Current isExpanded state:", isExpanded);
    }
  }, [currentRules, isDelta, isExpanded]);

  const toggleExpand = () => {
    console.log("Toggle expand clicked, current state:", isExpanded);
    setIsExpanded(prev => {
      const newState = !prev;
      console.log("New expanded state:", newState);
      return newState;
    });
  };

  return (
    <div>
      <Divider />
      <div className="text-2xl font-bold mt-6 mb-2">Rules</div>
      <style jsx>{`
        .rules-content {
          word-wrap: break-word !important;
          overflow-wrap: anywhere !important;
          word-break: break-all !important;
          hyphens: auto;
          white-space: pre-wrap;
          max-width: 100%;
          overflow: hidden;
          line-break: anywhere;
        }
        .rules-content * {
          max-width: 100% !important;
          word-wrap: break-word !important;
          overflow-wrap: anywhere !important;
          word-break: break-all !important;
          white-space: pre-wrap !important;
          line-break: anywhere !important;
        }
        .rules-content p,
        .rules-content div,
        .rules-content span {
          max-width: 100% !important;
          overflow: hidden !important;
          word-break: break-all !important;
          overflow-wrap: anywhere !important;
        }
        /* 特别针对中文和韩文的换行 */
        .rules-content :global(.prose) {
          word-break: break-all !important;
          overflow-wrap: anywhere !important;
          line-break: anywhere !important;
        }
      `}</style>
      <div
        ref={contentRef}
        className={`w-full rules-content ${isExpanded ? "overflow-visible" : "overflow-hidden"}`}
      >
        {isDelta ? (
          <QuillViewer delta={currentRules} isExpanded={isExpanded} className="break-words overflow-wrap-anywhere" />
        ) : (
          <div
            className="line-clamp-content break-words overflow-wrap-anywhere word-break"
            style={{
              maxHeight: isExpanded ? "none" : "4.5em", // 约3行的高度
              overflow: "hidden",
              lineHeight: "1.5em",
            }}
          >
            <div
              style={{
                display: "-webkit-box",
                WebkitLineClamp: isExpanded ? "unset" : 3,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {/* 将markdown内容转换为纯文本显示，避免多个p标签的问题 */}
              {isExpanded ? (
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  className="prose prose-sm max-w-none whitespace-pre-wrap"
                  components={{
                    p: ({ children }) => <p className="mb-2 break-words overflow-wrap-anywhere">{children}</p>,
                    div: ({ children }) => <div className="break-words overflow-wrap-anywhere">{children}</div>,
                    code: ({ children }) => <span className="break-words overflow-wrap-anywhere">{children}</span>,
                    pre: ({ children }) => (
                      <div className="break-words overflow-wrap-anywhere whitespace-pre-wrap">{children}</div>
                    ),
                  }}
                >
                  {currentRules}
                </ReactMarkdown>
              ) : (
                <div className="whitespace-pre-wrap break-words">
                  {(() => {
                    const text = currentRules.replace(/\n+/g, " ").trim();
                    // 如果文本很长，手动截断并添加省略号
                    if (text.length > 200) {
                      return text.substring(0, 200) + "...";
                    }
                    return text;
                  })()}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {uma_url && (
        <div className="px-4 py-2 flex items-center justify-between border-1 border-gray-200 rounded-lg gap-4 mt-4">
          <div className="flex items-center gap-4">
            <div className="font-bold text-xl font-serif text-red-500">UMA</div>
            <div className="flex flex-col">
              <div className="text-gray-500 text-sm">Resolver</div>
              <Link href={uma_url} isExternal className="flex items-center text-sm bg-white rounded-xl cursor-pointer">
                {transactionHash}
              </Link>
            </div>
          </div>

          <Button
            as={Link}
            href={uma_url}
            isExternal
            size="md"
            radius="full"
            className="bg-transparent border-1 hover:opacity-60 text-md font-semibold my-1 px-4"
          >
            Propose resolution
          </Button>
        </div>
      )}
      {needsExpansion && (
        <Button
          onPress={toggleExpand}
          size="md"
          radius="full"
          className="flex-1 bg-gray-200 hover:opacity-60 text-md font-semibold my-4"
        >
          {isExpanded ? "Show Less" : "Show More"}
        </Button>
      )}
    </div>
  );
};

export default RulesItem;
