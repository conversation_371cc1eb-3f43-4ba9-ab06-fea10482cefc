import React, { useEffect, useRef } from "react";
import Quill from "quill";
import "quill/dist/quill.snow.css";

interface QuillViewerProps {
  delta: any; // Quill Delta format
  className?: string;
  isExpanded?: boolean;
}

const QuillViewer: React.FC<QuillViewerProps> = ({ delta, className = "", isExpanded = false }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const quillRef = useRef<Quill | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // 清理之前的实例
    if (quillRef.current) {
      quillRef.current = null;
    }

    // 创建只读的Quill实例
    const quill = new Quill(containerRef.current, {
      theme: "snow",
      readOnly: true,
      modules: {
        toolbar: false, // 隐藏工具栏
      },
    });

    quillRef.current = quill;

    // 设置内容
    if (delta) {
      try {
        // 如果delta是字符串，尝试解析为JSON
        const deltaContent = typeof delta === "string" ? JSON.parse(delta) : delta;
        quill.setContents(deltaContent);
      } catch (error) {
        console.error("Error parsing delta content:", error);
        // 如果解析失败，将其作为纯文本处理
        quill.setText(typeof delta === "string" ? delta : JSON.stringify(delta));
      }
    }

    // 清理函数
    return () => {
      if (quillRef.current) {
        quillRef.current = null;
      }
    };
  }, [delta]);

  return (
    <div className={`quill-viewer ${className}`}>
      <div
        ref={containerRef}
        className={`
          break-words overflow-wrap-anywhere
          ${isExpanded ? "max-h-none overflow-visible" : "max-h-6 overflow-hidden"}
        `}
        style={{
          border: "none",
        }}
      />
      <style jsx>{`
        .quill-viewer :global(.ql-container) {
          border: none !important;
          font-size: inherit;
          font-family: inherit;
        }
        .quill-viewer :global(.ql-editor) {
          padding: 0 !important;
          border: none !important;
          outline: none !important;
          line-height: 1.5;
        }
        .quill-viewer :global(.ql-editor.ql-blank::before) {
          font-style: normal;
          color: #6b7280;
        }
        .quill-viewer :global(.ql-snow) {
          border: none !important;
        }
        .quill-viewer :global(.ql-toolbar) {
          display: none !important;
        }
        /* 确保富文本内容在容器内正确换行 */
        .quill-viewer :global(.ql-editor p) {
          margin: 0;
          word-wrap: break-word;
          overflow-wrap: break-word;
        }
        .quill-viewer :global(.ql-editor ul, .ql-editor ol) {
          padding-left: 1.5em;
        }
        .quill-viewer :global(.ql-editor blockquote) {
          border-left: 4px solid #ccc;
          margin-left: 0;
          margin-right: 0;
          padding-left: 16px;
          color: #666;
        }
      `}</style>
    </div>
  );
};

export default QuillViewer;
