import React, { useEffect, useState } from "react";
import EventCard from "./EventCard";
import { getAllTagsWithEventCount } from "@/api/events";
import useInfiniteScrollHome from "@/components/other/LoadMoreHome";
import {
  filterTagsByLanguage,
  filterTagsWithEventCount,
  getCurrentTimestamp,
  removeDuplicatesByTagSlug,
} from "@/utils";
import { Spinner } from "@heroui/react";
import { useTranslation } from "react-i18next";
import ScrollTab from "~~/components/events-components/ScrollTab";
import { useGlobalState } from "~~/services/store/store";

const EventCardList: React.FC = () => {
  const { current_language } = useGlobalState().nativeCurrency;
  const timeStamp = getCurrentTimestamp();
  const { t } = useTranslation();

  const [tagListData, setTagListData] = useState<any[]>([]);
  const [eventParams, setEventParams] = useState({
    tag_id: "all",
    limit: 16,
    offset: 0,
  });

  const {
    data: eventCardListData,
    hasMore,
    loadMoreRef,
    setQueryParams,
    setHasMore,
  } = useInfiniteScrollHome(eventParams);

  useEffect(() => {
    if (current_language === undefined) return;

    const allTag = {
      id: "all",
      label: t("Markets_Status_All") || "All",
      slug: "all",
      region: current_language,
    };

    getAllTagsWithEventCount({
      active: true,
      closed: false,
    })
      .then((res: any) => {
        const uniqueItems = removeDuplicatesByTagSlug(res.data.tags);
        const filteredByLanguage = filterTagsByLanguage(current_language, uniqueItems);

        // 使用新的组合过滤函数：过滤空标签和creator标签
        const filteredTags = filterTagsWithEventCount(filteredByLanguage, {
          filterEmpty: true,
          filterCreator: true,
          includeAdvancedFilters: false,
        });

        setTagListData([allTag, ...filteredTags]);

        // 切换语言区后，重置标签选择为"All"
        setEventParams(prev => ({
          ...prev,
          tag_id: "all",
          offset: 0,
        }));
      })
      .catch(error => {
        console.error("Error fetching tags with event count:", error);
      });
  }, [current_language, t]);

  // 当 eventParams 改变时，重置无限滚动
  useEffect(() => {
    setQueryParams({
      ...eventParams,
      offset: 0,
    });
    setHasMore(true);
  }, [eventParams, setQueryParams, setHasMore]);

  return (
    <div className="w-full flex flex-col items-center justify-center mb-12">
      <ScrollTab tagListData={tagListData} eventParams={eventParams} setEventParams={setEventParams} />

      {eventCardListData.length > 0 ? (
        <div className="w-full gap-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {eventCardListData.map((data, index) => (
            <EventCard
              key={`event-${data.id}-${index}-${eventParams.offset}`}
              eventCardData={data}
              timeStamp={timeStamp}
            />
          ))}
        </div>
      ) : (
        !hasMore && (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="text-gray-400 text-md mb-2">{t("Portfolio_No_More_Data") || "No more data"}</div>
          </div>
        )
      )}

      {/* 无限滚动加载更多触发器 */}
      <div ref={loadMoreRef} className="flex-center w-full h-16 mt-6">
        {hasMore ? (
          <div className="flex items-center gap-2 text-gray-500">
            <Spinner size="sm" color="default" />
            <span className="text-sm">{t("sports.loading_events") || "Loading events..."}</span>
          </div>
        ) : (
          eventCardListData.length > 0 && (
            <div className="text-gray-400 text-sm">{t("Portfolio_No_More_Data") || "No more data"}</div>
          )
        )}
      </div>
    </div>
  );
};
export default EventCardList;
