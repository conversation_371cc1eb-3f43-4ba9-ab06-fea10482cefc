import React, { useCallback, useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import ClaimRewardModal from "@/components/navbar/ClaimRewardModal";
import { useUserAddress } from "@/hooks/useUserAddress";
import { getItem } from "@/utils";
import { Spinner } from "@heroui/react";
import { BookOpen, Gift, Users, Volleyball } from "lucide-react";

export const SubHeaderNav: React.FC = () => {
  const router = useRouter();
  const [navigatingTo, setNavigatingTo] = useState<string | null>(null);
  const [targetPath, setTargetPath] = useState<string | null>(null);
  const pathname = usePathname();

  // 添加领取弹窗相关状态
  const [isClaimModalOpen, setIsClaimModalOpen] = useState(false);
  const { address } = useUserAddress();
  const proxyWallet = getItem(`login_proxyWallet`) || "";

  // 检查当前页面是否匹配
  const isCurrentPage = useCallback(
    (path: string) => {
      return pathname.startsWith(path);
    },
    [pathname],
  );

  // 通用导航处理函数
  const handleNavigation = useCallback(
    async (path: string, buttonName: string) => {
      setNavigatingTo(buttonName);
      setTargetPath(path);
      try {
        await router.push(path);
      } catch (error) {
        // 导航失败时清除loading状态
        setNavigatingTo(null);
        setTargetPath(null);
      }
    },
    [router],
  );

  // 监听路径变化，当到达目标路径时清除loading状态
  useEffect(() => {
    if (targetPath && pathname === targetPath && navigatingTo) {
      console.log(`🏁 Navigation completed to ${pathname}, clearing loading state`);
      setNavigatingTo(null);
      setTargetPath(null);
    }
  }, [pathname, targetPath, navigatingTo]);

  return (
    <div className="relative bg-transparent w-full">
      {/* 背景装饰 */}

      <div className="relative flex items-center min-h-14 md:min-h-12 px-3 md:px-6 w-full">
        <div className="flex items-center w-full min-h-14 md:min-h-12">
          {/* 导航项 */}
          <div
            className="flex space-x-1 md:space-x-4 items-center overflow-x-auto scrollbar-hide"
            style={{
              WebkitOverflowScrolling: "touch",
              scrollBehavior: "smooth",
            }}
          >
            {/* LIVE 指示器 - 移动端优化 */}
            <div className="flex items-center space-x-2 px-3 py-1.5 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-full flex-shrink-0">
              <div className="relative">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                <div className="absolute inset-0 w-2 h-2 bg-red-500 rounded-full animate-ping opacity-75" />
              </div>
              <span className="text-red-600 font-semibold text-sm">LIVE</span>
            </div>

            {/* Sports 按钮 - 移动端优化 */}
            <div
              className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
                navigatingTo === "sports"
                  ? "bg-gradient-to-r from-orange-500 to-red-500 text-white cursor-wait shadow-lg shadow-orange-500/25"
                  : isCurrentPage("/sports")
                  ? "bg-gradient-to-r from-orange-50 to-red-50 text-orange-600 border border-orange-200 shadow-md"
                  : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-orange-300 hover:shadow-md"
              }`}
              onClick={() => navigatingTo !== "sports" && handleNavigation("/sports", "sports")}
            >
              <div className="flex items-center space-x-2">
                {navigatingTo === "sports" ? (
                  <Spinner size="sm" className="text-white" />
                ) : (
                  <Volleyball size={16} className="group-hover:rotate-12 transition-transform duration-300" />
                )}
                <span className="font-medium text-sm">Sports</span>
              </div>
              {!navigatingTo && !isCurrentPage("/sports") && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-500/0 to-red-500/0 group-hover:from-orange-500/5 group-hover:to-red-500/5 transition-all duration-300" />
              )}
            </div>
            {/* RedPocket 按钮 - 移动端优化 */}
            <div
              className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
                navigatingTo === "redpocket"
                  ? "bg-gradient-to-r from-yellow-500 to-orange-500 text-white cursor-wait shadow-lg shadow-yellow-500/25"
                  : isCurrentPage("/redpocket")
                  ? "bg-gradient-to-r from-yellow-50 to-orange-50 text-yellow-600 border border-yellow-200 shadow-md"
                  : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-yellow-300 hover:shadow-md"
              }`}
              onClick={() => navigatingTo !== "redpocket" && handleNavigation("/redpocket", "redpocket")}
            >
              <div className="flex items-center space-x-2">
                {navigatingTo === "redpocket" ? (
                  <Spinner size="sm" className="text-white" />
                ) : (
                  <Gift size={16} className="group-hover:rotate-12 transition-transform duration-300" />
                )}
                <span className="font-medium text-sm">RedPocket</span>
              </div>
              {!navigatingTo && !isCurrentPage("/redpocket") && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-500/0 to-orange-500/0 group-hover:from-yellow-500/5 group-hover:to-orange-500/5 transition-all duration-300" />
              )}
            </div>
            {/* Learning Center 按钮 - 移动端显示 */}
            <div
              className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
                navigatingTo === "learning"
                  ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white cursor-wait shadow-lg shadow-green-500/25"
                  : isCurrentPage("/learning-center")
                  ? "bg-gradient-to-r from-green-50 to-emerald-50 text-green-600 border border-green-200 shadow-md"
                  : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-green-300 hover:shadow-md"
              }`}
              onClick={() => navigatingTo !== "learning" && handleNavigation("/learning-center", "learning")}
            >
              <div className="flex items-center space-x-1 md:space-x-2">
                {navigatingTo === "learning" ? (
                  <Spinner size="sm" className="text-white" />
                ) : (
                  <BookOpen
                    size={14}
                    className="md:w-4 md:h-4 group-hover:rotate-12 transition-transform duration-300"
                  />
                )}
                <span className="font-medium text-xs md:text-sm">
                  <span className="md:hidden">Learn</span>
                  <span className="hidden md:inline">Learning Center</span>
                </span>
              </div>
              {!navigatingTo && !isCurrentPage("/learning-center") && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500/0 to-emerald-500/0 group-hover:from-green-500/5 group-hover:to-emerald-500/5 transition-all duration-300" />
              )}
            </div>

            {/* Creator 按钮 - 移动端显示 */}
            <div
              className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 ${
                navigatingTo === "creator"
                  ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white cursor-wait shadow-lg shadow-purple-500/25"
                  : isCurrentPage("/creator")
                  ? "bg-gradient-to-r from-purple-50 to-pink-50 text-purple-600 border border-purple-200 shadow-md"
                  : "bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-purple-300 hover:shadow-md"
              }`}
              onClick={() => navigatingTo !== "creator" && handleNavigation("/creator", "creator")}
            >
              <div className="flex items-center space-x-1 md:space-x-2">
                {navigatingTo === "creator" ? (
                  <Spinner size="sm" className="text-white" />
                ) : (
                  <Users size={14} className="md:w-4 md:h-4 group-hover:rotate-12 transition-transform duration-300" />
                )}
                <span className="font-medium text-xs md:text-sm">Creator</span>
              </div>
              {!navigatingTo && !isCurrentPage("/creator") && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/0 to-pink-500/0 group-hover:from-purple-500/5 group-hover:to-pink-500/5 transition-all duration-300" />
              )}
            </div>

            {/* Prize Pool 按钮 */}
            <div
              className={`group relative flex items-center px-2 md:px-4 py-2 rounded-lg md:rounded-xl transition-all duration-300 flex-shrink-0 bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 cursor-pointer border border-gray-200 hover:border-amber-300 hover:shadow-md`}
              onClick={() => setIsClaimModalOpen(true)}
            >
              <div className="flex items-center space-x-1 md:space-x-2">
                <Gift size={14} className="md:w-4 md:h-4" />
                <span className="font-medium text-xs md:text-sm">Prize Pool</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 领取弹窗 */}
      <ClaimRewardModal
        isOpen={isClaimModalOpen}
        onClose={() => setIsClaimModalOpen(false)}
        address={address || ""}
        proxyWallet={proxyWallet}
      />
    </div>
  );
};
