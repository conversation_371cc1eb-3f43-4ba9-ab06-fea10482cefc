import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { claimAllRewards, getUserRewards } from "@/api/user";
import PreBuyModal from "@/components/details/signature/PreBuyModal";
import { getApprovalData, getItem } from "@/utils";
import { Button, Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
import { Gift } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

// 常量配置
const USDC_DECIMALS = Math.pow(10, 6);

// 类型定义
interface TokenInfo {
  symbol: string;
  name: string;
  decimals: number;
  logo: string;
  color: string;
}

interface RewardData {
  bounce_token_amount: string;
  bounce_token_contract: string;
  event_name: string;
  user_proxy_wallet: string;
}

interface AggregatedReward {
  amount: number;
  contract: string;
  tokenInfo: TokenInfo;
  events: string[];
}

interface ClaimRewardModalProps {
  isOpen: boolean;
  onClose: () => void;
  address: string;
  proxyWallet: string;
}

const ClaimRewardModal: React.FC<ClaimRewardModalProps> = ({ isOpen, onClose, address, proxyWallet }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { walletType } = useGlobalState();
  const clobApis = getItem("poly_clob_api_key_map");

  // 状态管理
  const [aggregatedRewards, setAggregatedRewards] = useState<Record<string, AggregatedReward>>({});
  const [totalValue, setTotalValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [buttonLoading, setButtonLoading] = useState(false);

  const [isShowPreBuyModal, setIsShowPreBuyModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");

  // 代币配置映射
  const TOKEN_CONTRACTS: Record<string, TokenInfo> = {
    "******************************************": {
      symbol: "USDC",
      name: "USD Coin",
      decimals: 6,
      logo: "$",
      color: "green",
    },
    // 其他未映射的代币统一归类
    other: {
      symbol: "OTHER",
      name: t("claimReward.otherRewards"),
      decimals: 18,
      logo: "🎁",
      color: "purple",
    },
  };

  // 工具函数：聚合奖励数据
  const aggregateRewards = (rewards: RewardData[]) => {
    const aggregated: Record<string, AggregatedReward> = {};
    let total = 0;

    rewards.forEach((reward: RewardData) => {
      const contract = reward.bounce_token_contract;
      const amount = parseFloat(reward.bounce_token_amount) / USDC_DECIMALS;

      // 检查是否为已知代币，未知的归类到"other"
      const isKnownToken = TOKEN_CONTRACTS[contract as keyof typeof TOKEN_CONTRACTS];
      const targetContract = isKnownToken ? contract : "other";

      if (aggregated[targetContract]) {
        aggregated[targetContract].amount += amount;
        aggregated[targetContract].events.push(reward.event_name);
      } else {
        const tokenInfo = isKnownToken
          ? TOKEN_CONTRACTS[contract as keyof typeof TOKEN_CONTRACTS]
          : TOKEN_CONTRACTS["other"];

        aggregated[targetContract] = {
          amount,
          contract: targetContract,
          tokenInfo,
          events: [reward.event_name],
        };
      }

      total += amount;
    });

    return { aggregated, total };
  };

  // API调用：获取用户奖励数据
  const fetchUserRewards = async () => {
    try {
      setLoading(true);

      // 调用API接口
      const response = await getUserRewards(proxyWallet);

      if (response.data && response.data.accumulate_lucky_money) {
        const rewards: RewardData[] = response.data.accumulate_lucky_money;
        const { aggregated, total } = aggregateRewards(rewards);

        setAggregatedRewards(aggregated);
        setTotalValue(total);
      }
    } catch (error) {
      console.error("Error fetching user rewards:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && proxyWallet) {
      fetchUserRewards();
    } else if (isOpen) {
      setAggregatedRewards({});
      setTotalValue(0);
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, proxyWallet]);

  // 事件处理：一键领取奖励
  const handleClaim = async () => {
    if (buttonLoading) return;
    setButtonLoading(true);
    setErrorMessage(""); // 清除之前的错误信息

    // 验证：检查API权限
    if (address) {
      const { hasCurrentApi, approved } = await getApprovalData(address);
      if (!hasCurrentApi || !approved) {
        setIsShowPreBuyModal(true);
        setButtonLoading(false);
        return;
      }
    }

    // 验证：检查是否有可领取的奖励
    if (Object.keys(aggregatedRewards).length === 0) {
      setErrorMessage(t("claimReward.noRewardsAvailable"));
      setButtonLoading(false);
      return;
    }

    try {
      const response = await claimAllRewards(clobApis, walletType);
      if (response.data?.success || response.status === 200) {
        setSuccessMessage(t("claimReward.claimSuccess"));
        setErrorMessage(""); // 清除错误信息
        // 重新获取数据
        await fetchUserRewards();
      } else {
        // 处理后端返回的错误信息
        let errorMsg = t("claimReward.claimFailedRetry");
        if (response.data?.error) {
          const error = response.data.error;
          if (error === "at least 1000000 is required") {
            errorMsg = t("claimReward.insufficientAmount");
          } else {
            errorMsg = error;
          }
        }
        setErrorMessage(errorMsg);
        setSuccessMessage(""); // 清除成功信息
      }
    } catch (error: any) {
      // 处理网络错误或其他异常
      let errorMsg = t("claimReward.networkError");
      if (error?.response?.data?.error) {
        const backendError = error.response.data.error;
        if (backendError === "at least 1000000 is required") {
          errorMsg = t("claimReward.insufficientAmount");
        } else {
          errorMsg = backendError;
        }
      } else if (error?.message) {
        errorMsg = `${t("claimReward.requestFailed")}: ${error.message}`;
      }
      setErrorMessage(errorMsg);
      setSuccessMessage(""); // 清除成功信息
    } finally {
      setButtonLoading(false);
    }
  };

  if (loading && proxyWallet) {
    return (
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="md"
        placement="center"
        classNames={{
          backdrop: "bg-gradient-to-t from-zinc-900/50 to-zinc-900/10 backdrop-blur-md",
          base: "border-none bg-gradient-to-br from-white to-default-200/20 dark:from-default-900 dark:to-default-900/20 backdrop-blur-md backdrop-saturate-150",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-t-lg">
            <div className="flex items-center gap-3">
              <Gift className="w-6 h-6 text-amber-600" />
              <span className="text-xl font-bold bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent">
                {t("claimReward.title")}
              </span>
            </div>
          </ModalHeader>
          <ModalBody className="pb-8 bg-gradient-to-br from-amber-50/30 to-yellow-50/30">
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <div className="relative">
                <div className="w-16 h-16 border-4 border-amber-200 rounded-full animate-spin border-t-amber-500"></div>
                <Gift className="absolute inset-0 m-auto w-6 h-6 text-amber-500" />
              </div>
              <p className="text-amber-700 font-medium text-lg">{t("claimReward.loading")}</p>
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce"></div>
                <div
                  className="w-2 h-2 bg-amber-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                ></div>
                <div
                  className="w-2 h-2 bg-amber-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                ></div>
              </div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="md"
        placement="center"
        classNames={{
          backdrop: "bg-gradient-to-t from-zinc-900/50 to-zinc-900/10 backdrop-blur-md",
          base: "border-none bg-gradient-to-br from-white to-default-200/20 dark:from-default-900 dark:to-default-900/20 backdrop-blur-md backdrop-saturate-150",
          header: "border-b-[1px] border-default-200/50",
          body: "",
          closeButton: "hover:bg-white/5 active:bg-white/10",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-t-lg">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Gift className="w-6 h-6 text-amber-600" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent">
                {t("claimReward.title")}
              </span>
            </div>
          </ModalHeader>
          <ModalBody className="pb-6 bg-gradient-to-br from-amber-50/30 to-yellow-50/30">
            {/* 奖励汇总总额显示 */}
            <div className="mb-6 p-6 rounded-xl bg-gradient-to-r from-amber-100/50 to-yellow-100/50 border border-amber-200/30 shadow-sm text-center">
              <div className="mb-2">
                <span className="text-lg font-semibold text-amber-800">{t("claimReward.totalAmount")}</span>
              </div>
              <div className="text-4xl font-bold text-amber-600 drop-shadow-sm mb-2">${totalValue.toFixed(6)}</div>
              <div className="text-sm text-amber-700">
                {t("claimReward.itemsCount", { count: Object.keys(aggregatedRewards).length })} {"·"}{" "}
                {t("claimReward.clickToClaim")}
              </div>
            </div>

            {/* 代币奖励卡片网格 */}
            <div className="grid grid-cols-2 gap-3 mb-6">
              {Object.keys(aggregatedRewards).length > 0 ? (
                Object.values(aggregatedRewards).map((reward: AggregatedReward, index: number) => {
                  const colorClasses = {
                    green: {
                      bg: "bg-gradient-to-br from-green-50 to-emerald-50",
                      border: "border-green-200",
                      logo: "bg-gradient-to-br from-green-500 to-emerald-600",
                      text: "text-green-800",
                      amount: "text-green-700",
                      glow: "shadow-green-100",
                    },
                    blue: {
                      bg: "bg-gradient-to-br from-blue-50 to-indigo-50",
                      border: "border-blue-200",
                      logo: "bg-gradient-to-br from-blue-500 to-indigo-600",
                      text: "text-blue-800",
                      amount: "text-blue-700",
                      glow: "shadow-blue-100",
                    },
                    purple: {
                      bg: "bg-gradient-to-br from-purple-50 to-violet-50",
                      border: "border-purple-200",
                      logo: "bg-gradient-to-br from-purple-500 to-violet-600",
                      text: "text-purple-800",
                      amount: "text-purple-700",
                      glow: "shadow-purple-100",
                    },
                    gray: {
                      bg: "bg-gradient-to-br from-gray-50 to-slate-50",
                      border: "border-gray-200",
                      logo: "bg-gradient-to-br from-gray-500 to-slate-600",
                      text: "text-gray-800",
                      amount: "text-gray-700",
                      glow: "shadow-gray-100",
                    },
                  };

                  const colors = colorClasses[reward.tokenInfo.color as keyof typeof colorClasses] || colorClasses.gray;

                  return (
                    <div
                      key={index}
                      className={`relative p-4 rounded-xl ${colors.bg} border ${colors.border} shadow-lg ${colors.glow} hover:shadow-xl hover:scale-105 transition-all duration-300 overflow-hidden`}
                    >
                      {/* 背景装饰 */}
                      <div className="absolute top-0 right-0 w-20 h-20 opacity-10">
                        <div className={`w-full h-full rounded-full ${colors.logo} blur-2xl`}></div>
                      </div>

                      <div className="relative flex items-center gap-3">
                        {/* Logo */}
                        <div
                          className={`w-12 h-12 rounded-full ${colors.logo} flex items-center justify-center flex-shrink-0 shadow-lg ring-2 ring-white/50`}
                        >
                          <span className="text-white font-bold text-xl drop-shadow-sm">{reward.tokenInfo.logo}</span>
                        </div>

                        {/* 奖品信息 */}
                        <div className="flex-1 min-w-0">
                          <div className={`font-semibold ${colors.text} text-sm mb-1`}>{reward.tokenInfo.symbol}</div>
                          <div className={`font-bold ${colors.amount} text-xl leading-tight drop-shadow-sm`}>
                            {reward.tokenInfo.symbol === "USDC"
                              ? `$${reward.amount.toFixed(6)}`
                              : reward.tokenInfo.symbol === "OTHER"
                              ? `${reward.amount.toFixed(6)}`
                              : reward.amount.toFixed(6)}
                          </div>
                        </div>

                        {/* 闪光效果 */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500 pointer-events-none transform -skew-x-12"></div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="col-span-2 p-8 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 text-center shadow-sm">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-2xl">🎁</span>
                  </div>
                  <button
                    onClick={() => {
                      onClose();
                      router.push("/redpocket");
                    }}
                    className="mt-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200"
                  >
                    {t("claimReward.claimFreeRewards")}
                  </button>
                </div>
              )}
            </div>

            {/* 消息提示 */}
            {errorMessage && (
              <div className="mb-6 p-4 bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-xl shadow-sm">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                    <span className="text-white text-sm font-bold">!</span>
                  </div>
                  <span className="text-red-800 text-sm font-medium flex-1">{errorMessage}</span>
                  <button
                    onClick={() => setErrorMessage("")}
                    className="ml-3 w-6 h-6 text-red-400 hover:text-red-600 hover:bg-red-100 rounded-full flex items-center justify-center transition-colors"
                  >
                    ✕
                  </button>
                </div>
              </div>
            )}

            {successMessage && (
              <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl shadow-sm">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                  <span className="text-green-800 text-sm font-medium flex-1">{successMessage}</span>
                  <button
                    onClick={() => setSuccessMessage("")}
                    className="ml-3 w-6 h-6 text-green-400 hover:text-green-600 hover:bg-green-100 rounded-full flex items-center justify-center transition-colors"
                  >
                    ✕
                  </button>
                </div>
              </div>
            )}

            {/* 一键领取按钮 */}
            <div className="relative">
              <Button
                onPress={handleClaim}
                disabled={buttonLoading || Object.keys(aggregatedRewards).length === 0}
                size="lg"
                radius="lg"
                className={`w-full font-bold text-lg py-4 transition-all duration-300 transform ${
                  buttonLoading || Object.keys(aggregatedRewards).length === 0
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl"
                } text-white border-none`}
                style={{
                  boxShadow:
                    buttonLoading || Object.keys(aggregatedRewards).length === 0
                      ? "none"
                      : "0 8px 25px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
                }}
              >
                <div className="flex items-center gap-3">
                  {buttonLoading ? (
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  ) : (
                    <Gift className="w-5 h-5" />
                  )}
                  <span className="drop-shadow-sm">
                    {buttonLoading
                      ? t("claimReward.claiming")
                      : Object.keys(aggregatedRewards).length === 0
                      ? t("claimReward.noRewardsButton")
                      : t("claimReward.claimButton")}
                  </span>
                </div>
              </Button>
              {/* 按钮光泽效果 */}
              {!buttonLoading && Object.keys(aggregatedRewards).length > 0 && (
                <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              )}
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>

      <PreBuyModal address={address} visible={isShowPreBuyModal} onClose={() => setIsShowPreBuyModal(false)} />
    </>
  );
};

export default ClaimRewardModal;
