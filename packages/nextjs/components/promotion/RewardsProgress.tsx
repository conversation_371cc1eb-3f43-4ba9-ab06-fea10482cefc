import React, { useEffect, useState } from "react";
import { getClaimReward, getRewardDistributionByProxyWallet, getWebUsersByProxyWallet } from "@/api/user";
import PreBuyModal from "@/components/details/signature/PreBuyModal";
import NotificationCard from "@/components/other/NotificationCard";
import { useGlobalState } from "@/services/store/store";
import { getApprovalData, getItem } from "@/utils";
import { Button, Card, Progress } from "@heroui/react";
import { useTranslation } from "react-i18next";

interface RewardsProgressProps {
  address: string;
  proxyWallet: string;
  isActiveWallet: boolean;
}

const SIXTH_POWER = Math.pow(10, 6);
const SEVENTH_POWER = Math.pow(10, 7);
const NINTH_POWER = Math.pow(10, 9);

const RewardsProgress: React.FC<RewardsProgressProps> = ({ address, proxyWallet, isActiveWallet }) => {
  const { t } = useTranslation();
  const clobApis = getItem("poly_clob_api_key_map");
  const { walletType } = useGlobalState();

  const [progress, setProgress] = useState(0);
  const [canClaim, setCanClaim] = useState(false);

  const [totalRewarded, setTotalRewarded] = useState(0);
  const [lastClaimTime, setLastClaimTime] = useState("");
  const [remainingVolume, setRemainingVolume] = useState(0);
  const [loading, setLoading] = useState(true);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [amount, setAmount] = useState(0);
  const [amount_processed, setAmountProcessed] = useState(0);
  const [pendingReward, setPendingReward] = useState(0);

  const [isShowPreBuyModal, setIsShowPreBuyModal] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [notiTitle, setNotiTitle] = useState<string>("");
  const [notiStatus, setNotiStatus] = useState<"success" | "failure">("success");
  const [notiContent, setNotiContent] = useState<string>("");

  const fetchRewardData = async () => {
    try {
      setLoading(true);

      const rewardResponse = await getRewardDistributionByProxyWallet(proxyWallet);
      const rewardData = rewardResponse.data.reward_distribution[0];
      const pendingReward = rewardData?.amount / SIXTH_POWER || 0;
      setAmount(rewardData.amount);
      setPendingReward(pendingReward);
      setAmountProcessed(rewardData.amount_processed);

      const userResponse = await getWebUsersByProxyWallet(proxyWallet);
      const totalVolume = userResponse.data.web_users[0].total_volume;

      const remainAmount = (rewardData?.amount_processed + SEVENTH_POWER) / 0.01 - totalVolume;
      const progressPercentage = Math.abs(remainAmount) / NINTH_POWER;
      const totalRewardedValue = rewardData?.amount_processed / SIXTH_POWER || 0;
      const lastClaim = rewardData.last_process_timestamp
        ? new Date(rewardData.last_process_timestamp).toLocaleString()
        : t("rewards.unknown");

      // completed case
      if (remainAmount <= 0) {
        setProgress(100);
        setTotalRewarded(totalRewardedValue);
        setLastClaimTime(lastClaim);
        setCanClaim(true);
        setRemainingVolume(0);
        return;
      }

      // uncompleted case
      if (progressPercentage >= 1) {
        setProgress(0);
        setTotalRewarded(totalRewardedValue);
        setLastClaimTime(lastClaim);
        setCanClaim(false);
        setRemainingVolume(Math.abs(remainAmount) / SIXTH_POWER);
        return;
      }

      // normal case
      setProgress((1 - progressPercentage) * 100);
      setTotalRewarded(totalRewardedValue);
      setLastClaimTime(lastClaim);
      setCanClaim(false);
      setRemainingVolume(Math.abs(remainAmount) / SIXTH_POWER);
    } catch (error) {
      console.error("Failed to fetch reward data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (proxyWallet) {
      fetchRewardData();
    } else {
      setProgress(0);
      setTotalRewarded(0);
      setLastClaimTime("");
      setCanClaim(false);
      setRemainingVolume(0);
      setAmount(0);
      setAmountProcessed(0);
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [proxyWallet]);

  const handleClaim = async () => {
    if (buttonLoading) return;
    setButtonLoading(true);

    if (address) {
      const { hasCurrentApi, approved } = await getApprovalData(address);
      if (!hasCurrentApi || !approved) {
        setIsShowPreBuyModal(true);
        setButtonLoading(false);
        return;
      }
    }

    // 超过最大可claim金额
    if (amount_processed + SEVENTH_POWER > amount) {
      setNotiTitle(t("rewards.claimFailure"));
      setNotiStatus("failure");
      setNotiContent("超过允许claim的金额最大值");
      setShowNotification(true);
      setButtonLoading(false);
      return;
    }

    try {
      const response = await getClaimReward(clobApis, walletType);

      if (response.status === 200) {
        const { amount_send } = response.data;
        setNotiTitle(t("rewards.claimSuccess"));
        setNotiStatus("success");
        setNotiContent(
          t("rewards.claimedAmount", {
            amount: (Number(amount_send) / SIXTH_POWER).toFixed(2),
          }),
        );
        await fetchRewardData();
        setShowNotification(true);
        setButtonLoading(false);
        return;
      }

      setNotiTitle(t("rewards.claimFailure"));
      setNotiStatus("failure");
      setNotiContent(t("rewards.claimFailureError"));
      setShowNotification(true);
    } catch (error: any) {
      setNotiTitle(t("rewards.claimFailure"));
      setNotiStatus("failure");
      setNotiContent(
        t("rewards.claimFailureError") +
          t("rewards.needMoreVolume", { volume: "$" + remainingVolume.toFixed(2), unit: t("rewards.transactions") }),
      );
      setShowNotification(true);
    } finally {
      setButtonLoading(false);
    }
  };

  if (loading && proxyWallet) {
    return (
      <div className="border rounded-lg overflow-hidden">
        <Card radius="none" className="p-6 max-w-md mx-auto w-full">
          <h2 className="text-lg font-bold mb-4">{t("rewards.title")}</h2>
          <div className="text-center text-gray-500">{t("rewards.loading")}</div>
        </Card>
      </div>
    );
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      <Card radius="none" className="p-6 max-w-md mx-auto w-full">
        <h2 className="text-lg font-bold mb-4 flex items-center">{t("rewards.title")}</h2>
        <div className="mb-4 text-gray-600 text-md">
          <span className="font-semibold">{t("rewards.pendingReward")}：</span>
          <span className="text-gray-600">{`$${pendingReward.toFixed(2)}`}</span>
        </div>
        <div className="mb-4 relative">
          <Progress
            value={progress}
            maxValue={100}
            minValue={0}
            size="lg"
            color="primary"
            radius="lg"
            showValueLabel={false}
            valueLabel={`${progress.toFixed(2)}%`}
            formatOptions={{ style: "percent" }}
            classNames={{
              base: "w-full",
              track: "h-6 drop-shadow-md border border-default",
              indicator: "bg-gradient-to-r from-pink-500 to-yellow-500",
            }}
          />
          <span
            className="absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2 text-gray-600 font-bold pointer-events-none"
            style={{ userSelect: "none" }}
          >
            {`${progress.toFixed(2)}%`}
          </span>
        </div>
        <div className="mb-2 text-sm text-gray-600">
          <span className="font-semibold">{t("rewards.totalRewarded")}：</span>
          <span>
            <span className="text-green-600 font-bold">{`$${totalRewarded.toFixed(2)}`}</span>
            <span className="mx-1 text-gray-400">/</span>
            <span className="text-gray-600">{`$${pendingReward.toFixed(2)}`}</span>
          </span>
        </div>
        <div className="mb-2 text-sm text-gray-600">
          <span className="font-semibold">{t("rewards.lastClaimTime")}：</span>
          <span className="">{lastClaimTime}</span>
        </div>

        <div className="text-sm text-gray-600">
          <span className="font-semibold">{t("rewards.remainingVolume")}：</span>
          {canClaim ? (
            <div className="text-sm text-green-600 font-semibold">{t("rewards.claimSuccessMessage")}</div>
          ) : (
            <span>{`$${remainingVolume.toFixed(2)} ${t("rewards.transactions")}`}</span>
          )}
        </div>

        {canClaim && isActiveWallet && (
          <Button
            onPress={handleClaim}
            disabled={buttonLoading}
            size="md"
            variant="bordered"
            radius="lg"
            className={`w-full font-semibold text-medium mt-4 ${
              buttonLoading ? "bg-gray-500" : "bg-green-600"
            } text-white font-bold py-2 px-4`}
          >
            {buttonLoading ? t("rewards.claiming") : t("rewards.claim")}
          </Button>
        )}
      </Card>

      {showNotification && (
        <NotificationCard
          title={notiTitle}
          content={notiContent}
          setShowNotification={setShowNotification}
          notiStatus={notiStatus}
        />
      )}

      <PreBuyModal address={address} visible={isShowPreBuyModal} onClose={() => setIsShowPreBuyModal(false)} />
    </div>
  );
};

export default RewardsProgress;
