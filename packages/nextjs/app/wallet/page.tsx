"use client";

import React, { useCallback, useEffect, useState } from "react";
import { getWebUsersByProxyWallet } from "@/api/user";
import PromotionTemporaryModal from "@/components/promotion/PromotionTemporaryModal";
// import RewardsProgress from "@/components/promotion/RewardsProgress";
import BalanceComponent from "@/components/wallet/Balance";
import DepositUsdc from "@/components/wallet/DepositUsdc";
import Enable from "@/components/wallet/Enable";
import LoginStep from "@/components/wallet/LoginStep";
import PointsAndFeesComponent from "@/components/wallet/PointsAndFees";
import { checkIfContract } from "@/contracts/checkIfContract";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import { usePromotionModal } from "@/hooks/usePromotionModal";
import { useUserAddress } from "@/hooks/useUserAddress";
import { getItem } from "@/utils";

const LoadingSpinner = () => (
  <div className="w-full text-center flex justify-center items-center">
    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
  </div>
);

const MainContent = ({
  isLoading,
  isActiveWallet,
  proxyWallet,
  isDisableBtn,
  setIsCreatedProxy,
  setIsActiveWallet,
}: {
  isLoading: boolean;
  isActiveWallet: boolean;
  proxyWallet: string;
  isDisableBtn: boolean;
  setIsCreatedProxy: (value: boolean) => void;
  setIsActiveWallet: (value: boolean) => void;
}) => {
  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (isActiveWallet) {
    return (
      <div className="w-full flex flex-col max-w-[660px]">
        <DepositUsdc />
      </div>
    );
  }

  return (
    <div className="w-full">
      <Enable
        proxyWallet={proxyWallet}
        isDisableBtn={isDisableBtn}
        setIsCreatedProxy={setIsCreatedProxy}
        setIsActiveWallet={setIsActiveWallet}
      />
    </div>
  );
};

const Sidebar = ({
  address,
  proxyWallet,
  isActiveWallet,
}: {
  address: string | undefined;
  proxyWallet: string;
  isActiveWallet: boolean;
}) => {
  if (!address) return null;

  return (
    <div className="w-full flex flex-col md:max-w-[340px] gap-4">
      <BalanceComponent address={address} proxyWallet={proxyWallet} isActiveWallet={isActiveWallet} />
      <PointsAndFeesComponent address={address} isActiveWallet={isActiveWallet} />
      {/* <RewardsProgress address={address} proxyWallet={proxyWallet} isActiveWallet={isActiveWallet} /> */}
    </div>
  );
};

const WalletPage: React.FC = () => {
  const { address, isConnected } = useUserAddress();
  const localProxyWallet = getItem(`login_proxyWallet`);
  const { promotionVisible, setPromotionVisible } = usePromotionModal();

  const [proxyWallet, setProxyWallet] = useState<string>(localProxyWallet || "");
  const [isActiveWallet, setIsActiveWallet] = useState<boolean>(false);
  const [isCreatedProxy, setIsCreatedProxy] = useState<boolean>(false);
  const [isDisableBtn, setIsDisableBtn] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const initializeProxyWallet = useCallback(async () => {
    if (!address) return;

    if (localProxyWallet) {
      setProxyWallet(localProxyWallet);
    } else {
      try {
        const computedProxyWallet = await computeProxyAddress(address);
        setProxyWallet(computedProxyWallet);
      } catch (error) {
        console.error("Error computing proxy address:", error);
      }
    }
  }, [address, localProxyWallet]);

  const checkProxyWalletStatus = useCallback(
    async (retryCount = 0) => {
      if (!proxyWallet) {
        setIsActiveWallet(false);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        const isContract = await checkIfContract(proxyWallet, setIsDisableBtn);
        if (!isContract) {
          // 如果是刚创建的代理钱包，可能需要等待区块链确认，进行重试
          if (isCreatedProxy && retryCount < 3) {
            setTimeout(() => {
              checkProxyWalletStatus(retryCount + 1);
            }, 2000 * (retryCount + 1)); // 递增延迟：2s, 4s, 6s
            return;
          }
          setIsActiveWallet(false);
          setIsLoading(false);
          return;
        }

        const webUsers = await getWebUsersByProxyWallet(proxyWallet);
        const hasWebUsers = webUsers.data.web_users.length > 0;

        // 如果合约存在但用户数据还没有同步，也进行重试
        if (!hasWebUsers && isCreatedProxy && retryCount < 3) {
          console.log(`User data not synced, retrying... (${retryCount + 1}/3)`);
          setTimeout(() => {
            checkProxyWalletStatus(retryCount + 1);
          }, 2000 * (retryCount + 1)); // 递增延迟：2s, 4s, 6s
          return;
        }

        setIsActiveWallet(hasWebUsers);
        setIsLoading(false); // 成功时设置 loading 为 false
      } catch (error) {
        console.error("Error checking proxy wallet status:", error);

        // 如果是刚创建的代理钱包且还有重试次数，进行重试
        if (isCreatedProxy && retryCount < 3) {
          console.log(`Error occurred, retrying... (${retryCount + 1}/3)`);
          setTimeout(() => {
            checkProxyWalletStatus(retryCount + 1);
          }, 2000 * (retryCount + 1));
          return;
        }

        setIsActiveWallet(false);
        setIsLoading(false); // 错误时设置 loading 为 false
      }
    },
    [proxyWallet, isCreatedProxy],
  );

  useEffect(() => {
    if (isConnected && address) {
      initializeProxyWallet();
    }
  }, [isConnected, address, initializeProxyWallet]);

  useEffect(() => {
    checkProxyWalletStatus();
  }, [checkProxyWalletStatus, isCreatedProxy]);

  return (
    <div className="flex justify-center pt-4 md:pt-12 px-4 sm:px-0 pb-20 md:pb-4">
      <PromotionTemporaryModal visible={promotionVisible} onClose={() => setPromotionVisible(false)} />
      <div className="w-full flex flex-col sm:flex-row max-w-[1024px] gap-4 md:gap-5">
        {!isConnected ? (
          <div className="w-full flex flex-col max-w-[660px]">
            <LoginStep />
          </div>
        ) : (
          <MainContent
            isLoading={isLoading}
            isActiveWallet={isActiveWallet}
            proxyWallet={proxyWallet}
            isDisableBtn={isDisableBtn}
            setIsCreatedProxy={setIsCreatedProxy}
            setIsActiveWallet={setIsActiveWallet}
          />
        )}

        <Sidebar address={address} proxyWallet={proxyWallet} isActiveWallet={isActiveWallet} />
      </div>
    </div>
  );
};

export default WalletPage;
