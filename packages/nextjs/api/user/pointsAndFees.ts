import { initRequest } from "../request";

// 积分和手续费数据类型
export interface PointsAndFeesData {
  user_wallet: string;
  proxy_wallet: string;
  fee: number;
  credit: number;
  credit_ratio: number;
}

/**
 * 获取用户积分和手续费数据
 * @param user_wallet 用户钱包地址
 * @returns Promise<PointsAndFeesData | null>
 */
export const getPointsAndFees = async (user_wallet: string): Promise<PointsAndFeesData | null> => {
  try {
    const response = await initRequest({
      url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getPointsAndFees`,
      method: "get",
      headers: { "Content-Type": "application/json" },
      params: {
        user_wallet: user_wallet,
      },
    });

    return response?.data || null;
  } catch (error) {
    console.error("Error fetching points and fees:", error);
    throw error;
  }
};

/**
 * 获取多个用户的积分和手续费数据
 * @param user_wallets 用户钱包地址数组
 * @returns Promise<PointsAndFeesData[]>
 */
export const getBatchPointsAndFees = async (user_wallets: string[]): Promise<PointsAndFeesData[]> => {
  try {
    const response = await initRequest({
      url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getBatchPointsAndFees`,
      method: "get",
      headers: { "Content-Type": "application/json" },
      params: {
        user_wallets: user_wallets.join(","),
      },
    });

    return response?.data || [];
  } catch (error) {
    console.error("Error fetching batch points and fees:", error);
    throw error;
  }
};

/**
 * 更新用户积分数据
 * @param user_wallet 用户钱包地址
 * @param credit 新的积分值
 * @param credit_ratio 新的积分系数
 * @returns Promise<boolean>
 */
export const updateUserCredits = async (
  user_wallet: string,
  credit: number,
  credit_ratio: number,
): Promise<boolean> => {
  try {
    const response = await initRequest({
      url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/updateUserCredits`,
      method: "post",
      headers: { "Content-Type": "application/json" },
      data: {
        user_wallet,
        credit,
        credit_ratio,
      },
    });

    return response?.data?.success || false;
  } catch (error) {
    console.error("Error updating user credits:", error);
    throw error;
  }
};

/**
 * 更新用户手续费数据
 * @param user_wallet 用户钱包地址
 * @param fee 新的手续费值
 * @returns Promise<boolean>
 */
export const updateUserFees = async (user_wallet: string, fee: number): Promise<boolean> => {
  try {
    const response = await initRequest({
      url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/updateUserFees`,
      method: "post",
      headers: { "Content-Type": "application/json" },
      data: {
        user_wallet,
        fee,
      },
    });

    return response?.data?.success || false;
  } catch (error) {
    console.error("Error updating user fees:", error);
    throw error;
  }
};
