import { useCallback, useEffect, useState } from "react";
import { PointsAndFeesData, getPointsAndFees } from "@/api/user/pointsAndFees";

interface UsePointsAndFeesReturn {
  data: PointsAndFeesData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for fetching user points and fees data
 * @param user_wallet 用户钱包地址
 * @param enabled 是否启用自动获取数据
 * @returns UsePointsAndFeesReturn
 */
export const usePointsAndFees = (user_wallet: string | null, enabled = true): UsePointsAndFeesReturn => {
  const [data, setData] = useState<PointsAndFeesData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!user_wallet || !enabled) return;

    setLoading(true);
    setError(null);

    try {
      const result = await getPointsAndFees(user_wallet);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch points and fees");
      console.error("Error in usePointsAndFees:", err);
    } finally {
      setLoading(false);
    }
  }, [user_wallet, enabled]);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
  };
};

/**
 * Hook for fetching points and fees with manual trigger
 * @returns UsePointsAndFeesReturn with manual fetch function
 */
export const usePointsAndFeesLazy = () => {
  const [data, setData] = useState<PointsAndFeesData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPointsAndFees = useCallback(async (user_wallet: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await getPointsAndFees(user_wallet);
      setData(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch points and fees";
      setError(errorMessage);
      console.error("Error in usePointsAndFeesLazy:", err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    if (data?.user_wallet) {
      await fetchPointsAndFees(data.user_wallet);
    }
  }, [data?.user_wallet, fetchPointsAndFees]);

  return {
    data,
    loading,
    error,
    fetchPointsAndFees,
    refetch,
  };
};
