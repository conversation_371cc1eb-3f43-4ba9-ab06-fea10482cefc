import { createRpc<PERSON><PERSON>ider } from "@/utils/rpc/rpcClient";
import { Interface } from "ethers";

const proxyFactoryAddress = process.env.NEXT_PUBLIC_FACTORY_ADDRESS;
if (!proxyFactoryAddress) {
  throw new Error("NEXT_PUBLIC_FACTORY_ADDRESS is not defined");
}

const abi = ["function computeProxyAddress(address user) public view returns (address)"];

async function computeProxyAddress(address: string): Promise<string> {
  try {
    const provider = createRpcProvider();
    const iface = new Interface(abi);
    const data = iface.encodeFunctionData("computeProxyAddress", [address]);

    const result = await provider.call({ to: proxyFactoryAddress, data });
    const decodedResult = iface.decodeFunctionResult("computeProxyAddress", result);

    return decodedResult[0];
  } catch (error: any) {
    console.error("Error fetching proxy address:", error);
    return "";
  }
}

export default computeProxyAddress;
