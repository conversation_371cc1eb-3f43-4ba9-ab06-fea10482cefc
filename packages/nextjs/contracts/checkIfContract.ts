import { createRpcProvider } from "@/utils/rpc/rpcClient";
import { addToast } from "@heroui/react";

export async function checkIfContract(address: string, setIsDisableBtn?: any): Promise<boolean> {
  try {
    const provider = createRpcProvider();
    const code = await provider.send("eth_getCode", [address, "latest"]);

    setIsDisableBtn && setIsDisableBtn(false);
    return code.length > 2;
  } catch (error: any) {
    console.error("Error checking if address is a contract:", error);
    addToast({
      title: "Query contract address failed",
      description: "Please refresh the page",
      color: "danger",
      timeout: 5000,
    });
    setIsDisableBtn && setIsDisableBtn(true);
    return false;
  }
}
